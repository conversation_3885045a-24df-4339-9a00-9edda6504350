import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';

import '../../../core/consts/api_strings.dart';
import '../../shipping/model/city_cost_model.dart';

class CountryModel {
  final int? id;
  final String? documentId;
  final String? name;
  final String? nameAr;
  final BaseMediaModel? icon;
  final List<CityCostModel> cities;

  CountryModel({
    this.id,
    this.documentId,
    this.name,
    this.nameAr,
    this.icon,
    this.cities = const [],
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      name: json[ApiStrings.name] ?? '',
      nameAr: json[ApiStrings.nameAr] ?? '',
      icon: json[ApiStrings.icon] != null
          ? BaseMediaModel.fromJson(json[ApiStrings.icon])
          : null,
      cities: (json[ApiStrings.cities] as List?)
              ?.map((e) => CityCostModel.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (documentId != null) ApiStrings.documentId: documentId,
      if (name != null) ApiStrings.name: name,
    };
  }
}
