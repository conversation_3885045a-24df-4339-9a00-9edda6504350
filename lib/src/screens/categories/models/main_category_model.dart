import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared_models/base_media_model.dart';

class MainCategoryModel {
  final int? id;
  final String? documentId;
  final String? englishName;
  final String? arabicName;
  final BaseMediaModel? featureImage;
  final List<CategoryModel> categories;
  final int? sort;

  MainCategoryModel({
    this.categories = const [],
    this.id,
    required this.englishName,
    this.arabicName,
    this.documentId,
    this.featureImage,
    this.sort,
  });

  //! name by lang
  String nameByLang(BuildContext context) {
    final isEnglish = context.readIsEng;

    return isEnglish
        ? (englishName?.isNotEmpty == true ? englishName! : arabicName ?? '')
        : (arabicName?.isNotEmpty == true ? arabicName! : englishName ?? '');
  }

  //! from Json
  factory MainCategoryModel.fromJson(Map<String, dynamic> json) {
    if (json.isEmpty) return MainCategoryModel.empty();

    final categories = json[ApiStrings.categories] ?? [];

    final categoryList = List<CategoryModel>.from(
        categories.map((category) => CategoryModel.fromJson(category)));

    return MainCategoryModel(
        id: json[ApiStrings.id],
        englishName: json[ApiStrings.name],
        arabicName: json[ApiStrings.nameAr],
        documentId: json[ApiStrings.documentId],
        featureImage: BaseMediaModel.fromJson(json[ApiStrings.featureImage]),
        sort: json[ApiStrings.sort],
        categories: categoryList);
  }

  //! from empty
  factory MainCategoryModel.empty() {
    return MainCategoryModel(
        englishName: null,
        arabicName: null,
        documentId: '',
        sort: 0,
        categories: [],
        featureImage: BaseMediaModel.empty());
  }

  //? Copy with
  MainCategoryModel copyWith({
    int? id,
    String? documentId,
    String? englishName,
    String? arabicName,
    BaseMediaModel? featureImage,
    List<CategoryModel>? categories,
    int? sort,
  }) {
    return MainCategoryModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      englishName: englishName ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      featureImage: featureImage ?? this.featureImage,
      categories: categories ?? this.categories,
      sort: sort ?? this.sort,
    );
  }

  // to json
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data[ApiStrings.id] = id;
    }

    if (documentId != null) {
      data[ApiStrings.documentId] = documentId;
    }

    data[ApiStrings.name] = englishName;

    data[ApiStrings.nameAr] = arabicName;

    if (categories.isNotEmpty) {
      data[ApiStrings.categories] =
          categories.map((category) => category.id).toList();
    }

    if (sort != null) {
      data[ApiStrings.sort] = sort;
    }

    return data;
  }

  @override
  String toString() => 'MainCatName $englishName  catId $documentId ';
}
