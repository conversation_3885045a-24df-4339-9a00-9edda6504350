import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared_models/base_media_model.dart';
import '../../products/models/products_model.dart';

class CategoryModel {
  final int? id;
  final String? documentId;
  final String? name;
  final String? nameAr;
  final BaseMediaModel? featureImage;

  // final List<ProductModel>? products;
  final String? createdAt;
  final int? sortNumber;
  final MainCategoryModel? mainCategory;

  CategoryModel(
      {
      // this.products,
      this.id,
      this.name,
      this.nameAr,
      this.documentId,
      this.mainCategory,
      this.featureImage,
      this.createdAt,
      this.sortNumber});

  // name by lang
  String nameByLang(BuildContext context) {
    final isEnglish = context.readIsEng;

    return isEnglish
        ? (name?.isNotEmpty == true ? name! : nameAr ?? '')
        : (nameAr?.isNotEmpty == true ? nameAr! : name ?? '');
  }

  //! from Json
  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    if (json.isEmpty) return CategoryModel.empty();

    final mainCategory = json[ApiStrings.mainCategory] != null
        ? MainCategoryModel.fromJson(json[ApiStrings.mainCategory])
        : null;

    return CategoryModel(
        id: json[ApiStrings.id],
        name: json[ApiStrings.name],
        nameAr: json[ApiStrings.nameAr],
        documentId: json[ApiStrings.documentId],
        mainCategory: mainCategory,
        featureImage: BaseMediaModel.fromJson(json[ApiStrings.featureImage]),
        createdAt: json[ApiStrings.createdAt],
        // products: productCategoryList,
        sortNumber: json[ApiStrings.sort]);
  }

  //! from empty
  factory CategoryModel.empty() {
    return CategoryModel(
        name: null,
        nameAr: null,
        documentId: '',
        sortNumber: 0,
        featureImage: BaseMediaModel.empty());
  }

  //? Copy with
  CategoryModel copyWith({
    int? id,
    String? documentId,
    String? name,
    String? arabicName,
    BaseMediaModel? featureImage,
    List<ProductModel>? products,
    int? sortNumber,
    MainCategoryModel? mainCategory,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      name: name ?? this.name,
      nameAr: arabicName ?? this.nameAr,
      featureImage: featureImage ?? this.featureImage,
      sortNumber: sortNumber ?? this.sortNumber,
      mainCategory: mainCategory ?? this.mainCategory,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data[ApiStrings.id] = id;
    }

    if (documentId != null) {
      data['documentId'] = documentId;
    }

    if (documentId != null) {
      data[ApiStrings.documentId] = documentId;
    }

    data[ApiStrings.name] = name;
    data[ApiStrings.sort] = sortNumber;

    data[ApiStrings.nameAr] = nameAr;
    // data[ApiStrings.mainCategory] = mainCategory;

    if (mainCategory != null && mainCategory!.documentId != null) {
      data['main_category'] = mainCategory!.id;
    }

    return data;
  }

  @override
  String toString() => 'CatName $name  catId $documentId ';
}
