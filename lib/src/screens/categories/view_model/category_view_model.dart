import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/bottom_nav_provider.dart';
import 'package:provider/provider.dart';

import '../../home/<USER>/main_screen.dart';
import '../models/category_model.dart';
import '../models/filtered_main_category_model.dart';
import '../models/main_category_model.dart';
import '../repository/category_repository.dart';

class CategoryVM extends BaseVM {
  CategoryRepository categoryRepository;

  CategoryVM(this.categoryRepository);

  var categories = <CategoryModel>[];

  var searchedCategories = <CategoryModel>[];
  FilteredMainCategoryModel? filteredMainCategoryData;
  FilteredMainCategoryModel? searchedFilteredData;

  bool get isThereOneMainCategory =>
      categories.any((element) => element.mainCategory != null);

  String? getCategoryName(
      {required CategoryModel category, required BuildContext context}) {
    final categoryName = context.isEng
        ? (category.name?.isNotEmpty == true
            ? category.name!
            : category.nameAr ?? '')
        : (category.nameAr?.isNotEmpty == true
            ? category.nameAr!
            : category.name ?? '');

    return categoryName;
  }

  bool _matchesQuery(String? englishName, String? arabicName, String query) {
    if (query.isEmpty) return true;
    final lowerQuery = query.toLowerCase();
    return (englishName?.toLowerCase().contains(lowerQuery) ?? false) ||
        (arabicName?.toLowerCase().contains(lowerQuery) ?? false);
  }

  getSearchedCategories(String query) {
    // Keep the old logic for backward compatibility
    searchedCategories = categories
        .where((element) =>
            (element.name?.toLowerCase().contains(query.toLowerCase()) ??
                false) ||
            (element.nameAr?.toLowerCase().contains(query.toLowerCase()) ??
                false))
        .toList();

    // New enhanced search logic for the filtered data structure
    if (query.isEmpty) {
      searchedFilteredData = null;
      notifyListeners();
      return;
    }

    // Filter main categories and their subcategories
    List<MainCategoryModel> filteredMainCategories = [];
    for (var mainCategory
        in filteredMainCategoryData?.mainCategories ?? <MainCategoryModel>[]) {
      bool mainCategoryMatches = _matchesQuery(
          mainCategory.englishName, mainCategory.arabicName, query);

      // Filter subcategories that match the query
      List<CategoryModel> matchingSubcategories = mainCategory.categories
          .where((cat) => _matchesQuery(cat.name, cat.nameAr, query))
          .toList();

      // Include main category if it matches or has matching subcategories
      if (mainCategoryMatches || matchingSubcategories.isNotEmpty) {
        // If main category matches, include all subcategories, otherwise only matching ones
        List<CategoryModel> subcategoriesToInclude = mainCategoryMatches
            ? mainCategory.categories
            : matchingSubcategories;

        filteredMainCategories
            .add(mainCategory.copyWith(categories: subcategoriesToInclude));
      }
    }

    // Filter standalone categories (categories without main category)
    List<CategoryModel> filteredStandaloneCategories =
        (filteredMainCategoryData?.categories ?? [])
            .where((cat) => _matchesQuery(cat.name, cat.nameAr, query))
            .toList();

    searchedFilteredData = FilteredMainCategoryModel(
      mainCategories: filteredMainCategories,
      categories: filteredStandaloneCategories,
    );

    notifyListeners();
  }

  void clearCategories() {
    categories.clear();

    notifyListeners();
  }

  bool isCategoriesNotEmpty = false;

  Future<void> getFilteredMainCategories(
    BuildContext context,
  ) async {
    await baseFunction(context, () async {
      filteredMainCategoryData =
          await categoryRepository.getFilteredMainCategories();

      categories = filteredMainCategoryData!.categories.map(
        (e) {
          final categoryMainCategory = filteredMainCategoryData?.mainCategories
              .firstWhereOrNull((element) => element.categories
                  .map(
                    (e) => e.documentId,
                  )
                  .contains(e.documentId));

          return e.copyWith(mainCategory: categoryMainCategory);
        },
      ).toList();

      // Add categories from main categories too but without documentId duplication
      categories.addAll(filteredMainCategoryData!.mainCategories
          .expand((element) => element.categories)
          .where((element) =>
              !categories.map((e) => e.documentId).contains(element.documentId))
          .map((e) {
        final categoryMainCategory = filteredMainCategoryData?.mainCategories
            .firstWhereOrNull((element) => element.categories
                .map(
                  (e) => e.documentId,
                )
                .contains(e.documentId));

        return e.copyWith(mainCategory: categoryMainCategory);
      }).toList());
    });
  }

  //! Get categories ====================================
  Future<void> getCategories(
    BuildContext context, {
    bool withLoading = true,
  }) async {
    await baseFunction(context, () async {
      // categories = await categoryRepository.getCategories();

      await getFilteredMainCategories(context);

      isAllCategoriesSortNumberEqualNull();
      addSortNumbersToCategories(categories);
    }, isLoading: withLoading);
  }

  //! Add category ====================================
  Future<void> addCategory(BuildContext context,
      {required String name,
      required String arabicName,
      required String pickedImage}) async {
    await baseFunction(context, () async {
      final cat = CategoryModel(
        nameAr: arabicName,
        name: name,
        sortNumber: categories.length + 1,
      );

      await categoryRepository.addCategory(cat: cat, pickedImage: pickedImage);
    }, type: FlushBarType.add, additionalFunction: getFilteredMainCategories);
  }

  //! Edit category ====================================
  Future<void> editCategory(BuildContext context,
      {required CategoryModel cat,
      String? pickedImage,
      bool getCategoriesAfterEdit = true}) async {
    await baseFunction(context, () async {
      await categoryRepository.editCategory(cat: cat, pickedImage: pickedImage);
    },
        type: getCategoriesAfterEdit ? FlushBarType.update : null,
        additionalFunction:
            getCategoriesAfterEdit ? getFilteredMainCategories : null);
  }

  //! Delete category ====================================
  Future<void> deleteCategory(
    BuildContext context, {
    required CategoryModel category,
  }) async {
    await baseFunction(
      context,
      () async {
        final categoryData = categories.firstWhereOrNull(
          (element) => element.documentId == category.documentId,
        );

        if (categoryData == null) {
          return;
        }

        await categoryRepository.deleteCategory(category: categoryData);

        await getFilteredMainCategories(context);
      },
      type: FlushBarType.delete,
      additionalFunction: _navigateToCatScreen,
    );
  }

  void _navigateToCatScreen(BuildContext context) {
    context.read<BottomNavbarVM>().setCurrentIndex(1);
    context.toReplacement(const MainScreen());
  }

  bool isAllCategoriesSortNumberEqualNull() {
    final isAllCategoriesSortNumberEqualNull =
        categories.every((element) => element.sortNumber == null);

    Log.f(
        "isAllCategoriesSortNumberEqualNull: $isAllCategoriesSortNumberEqualNull");

    return isAllCategoriesSortNumberEqualNull;
  }

  Future<void> addSortNumbersToCategories(
      List<CategoryModel> categories) async {
    if (isAllCategoriesSortNumberEqualNull() && categories.isNotEmpty) {
      for (int i = 0; i < categories.length; i++) {
        categories[i] = categories[i].copyWith(sortNumber: i + 1);
        await categoryRepository.editCategory(cat: categories[i]);
      }

      Log.f(
          "categories SortNumber: ${categories.map((e) => e.sortNumber).toList()}");
    }

    notifyListeners();
  }

  //! Bulk Update Categories ============================================
  Future<void> bulkUpdateCategories(
    BuildContext context, {
    required Map<String, Map<String, dynamic>> categoriesData,
  }) async {
    await baseFunction(
      context,
      () async {
        await categoryRepository.bulkUpdateCategories(
          categoriesData: categoriesData,
        );
      },
      type: FlushBarType.update,
    );
  }
}
