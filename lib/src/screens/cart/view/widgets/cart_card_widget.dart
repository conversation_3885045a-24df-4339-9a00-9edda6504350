import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/colors/choose_color_card.dart';
import 'package:provider/provider.dart';

import '../../../../../generated/assets.dart';
import '../../../../core/resources/app_radius.dart';
import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/icon_widget/icon_widget.dart';
import '../../../dashboard/models/extra_setting_model.dart';
import 'cart_quantity_buttons.dart';

class CartCardWidget extends StatelessWidget {
  final ProductQuantityModel cart;

  const CartCardWidget({super.key, required this.cart});

  @override
  Widget build(BuildContext context) {
    final isColorFound = cart.color.isNotEmpty;
    final isSizeFound = cart.size.isNotEmpty;
    final isExtrasFound = cart.extras.isNotEmpty;

    return Container(
      padding: const EdgeInsets.only(
        top: AppSpaces.mediumPadding,
        left: AppSpaces.mediumPadding,
        right: AppSpaces.mediumPadding,
      ),
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.circular(AppRadius.baseRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //! product image
          Column(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                child: Image.network(
                  cart.product?.thumbnail?.url ?? '',
                  fit: BoxFit.cover,
                  height: 75.h,
                  errorBuilder: (context, error, stackTrace) {
                    return const IconWidget(icon: Assets.iconsPickImage);
                  },
                ),
              ),
              TextButton(
                onPressed: () {
                  context.read<CartVM>().deleteFromCart(id: cart.id);
                },
                child: Row(
                  children: [
                    const Icon(
                      CupertinoIcons.delete,
                      color: ColorManager.errorColor,
                      size: 18,
                    ),
                    context.xSmallGap,
                    Text(
                      context.tr.delete,
                      style: context.labelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ColorManager.errorColor,
                      ),
                    ),
                  ],
                ),
                // CircleAvatar(
                //     backgroundColor:
                //         ColorManager.primaryColor.withOpacity(0.7),
                //     radius: 15.r,
                //     child: const Icon(
                //       Icons.delete_rounded,
                //       color: Colors.white,
                //       size: 18,
                //     )),
              ),
            ],
          ),
          context.mediumGap,
          //! product name & price
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Product name
                Text(
                  cart.nameByLang(context),
                  style: context.labelLarge,
                ),

                context.smallGap,

                //! Price & Sale Price
                Text(
                  (cart.totalPrice.toString()).toCurrency(context),
                  style: context.title.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),

                context.smallGap,

                //! Size & Color
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (isSizeFound) ...[
                          Text(
                            '${context.tr.size}: ${cart.size}',
                            style: context.labelLarge.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          context.smallGap,
                        ],
                        if (isColorFound) ...[
                          Text(
                            '${context.tr.color}:',
                            style: context.labelLarge.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          context.smallGap,
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: ChooseColorCard(
                                color: ExtraSettingsModel(
                                    englishName: cart.color)),
                          )
                        ],
                      ],
                    ),
                    if (isExtrasFound) ...[
                      context.smallGap,
                      Text(
                        '${context.tr.extras}: ${cart.extras.map((e) {
                          final nameByLang = context.isEng
                              ? e['name'] ?? e['name_ar'] ?? ''
                              : e['name_ar'] ?? e['name'] ?? '';
                          return '$nameByLang${e['price'] != null && e['price'] != 0 ? ' (${e['price'].toString().toNum()?.toCurrency(context)})' : ''}';
                        }).join(', ')}',
                        style: context.labelLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ]
                  ],
                ),
              ],
            ),
          ),
          CartQuantityButtons(
            quantity: cart.quantity!,
            productQuantityModel: cart,
          ),
        ],
      ),
    );
  }
}
