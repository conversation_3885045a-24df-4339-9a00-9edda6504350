import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/cached_images/main_cached_image.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/colors/choose_color_card.dart';
import 'package:provider/provider.dart';

class OrderProductDetailsWidget extends StatelessWidget {
  final ProductQuantityModel productQuantity;

  const OrderProductDetailsWidget({super.key, required this.productQuantity});

  @override
  Widget build(BuildContext context) {
    final product = productQuantity.product ?? ProductModel();

    final size = productQuantity.size.isEmpty ? '' : productQuantity.size;

    return Consumer<MainCategoryVM>(
      builder: (context, mainCategoryVM, child) {
        final productCategoryMainCategory =
            mainCategoryVM.mainCategories.firstWhereOrNull(
          (element) =>
              element.categories.firstWhereOrNull(
                  (e) => e.documentId == product.category?.documentId) !=
              null,
        );

        if (product.documentId == null) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (productQuantity.nameByLang(context).isNotEmpty) ...[
                        Text(
                          productQuantity.nameByLang(context),
                          style: context.subTitle,
                        ),
                        context.xSmallGap,
                      ],
                      Text(
                        '(${context.tr.thisProductIsDeleted})',
                        style: context.labelLarge
                            .copyWith(color: ColorManager.errorColor),
                      ),
                    ],
                  ),
                  if (size.isNotEmpty)
                    Text(
                      '${context.tr.size}: $size',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: context.subTitle,
                    ),
                  if (productQuantity.color.isNotEmpty) ...[
                    Row(
                      children: [
                        Text(
                          '${context.tr.color}:',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: context.subTitle,
                        ),
                        ChooseColorCard(
                          color: ExtraSettingsModel(
                            englishName: productQuantity.color,
                          ),
                        ).sized(height: 20),
                      ],
                    ),
                  ],
                  if (productQuantity.extras.isNotEmpty) ...[
                    Text(
                      '${context.tr.extras}: ${productQuantity.extras.map((e) => '${e['name']} (+${e['price']})').join(', ')}',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: context.subTitle,
                    ),
                  ],
                ],
              ),

              //! Quantity
              Text(
                "x${productQuantity.quantity}",
                style: context.greyLabelMedium.copyWith(fontSize: 16),
              ),
            ],
          );
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(5)),
              child: BaseCachedImage(
                product.thumbnail?.url ?? '',
                fit: BoxFit.cover,
                height: 70.h,
                width: 80.w,
              ),
            ),
            context.smallGap,
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          productQuantity.nameByLang(context),
                          //productVM.getProductTitle(
                          //                                     product: product, context: context) ?? ''
                          style: context.labelLarge,
                        ),

                        Row(
                          children: [
                            if (size.isNotEmpty) ...[
                              Text(
                                '${context.tr.size}: $size',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: context.labelLarge,
                              ),
                              context.mediumGap,
                            ],
                            if (productQuantity.color.isNotEmpty) ...[
                              Row(
                                children: [
                                  Text(
                                    '${context.tr.color}:',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                    style: context.labelLarge,
                                  ),
                                  ChooseColorCard(
                                    color: ExtraSettingsModel(
                                      englishName: productQuantity.color,
                                    ),
                                  ).sized(height: 20),
                                ],
                              ),
                            ],
                          ],
                        ),
                        if (productQuantity.extras.isNotEmpty) ...[
                          Text(
                            '${context.tr.extras}: ${productQuantity.extras.map((e) {
                              final nameByLang = context.isEng
                                  ? e['name'] ?? e['name_ar']
                                  : e['name_ar'] ?? e['name'];

                              return '$nameByLang ${e['price'] == null ? '' : '(${e['price']?.toString().toNum()?.toCurrency(context) ?? ''})'}';
                            }).join(', ')}',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            style: context.labelLarge,
                          ),
                        ],
                        if (productCategoryMainCategory != null)
                          Text(
                            productCategoryMainCategory.nameByLang(context),
                            style: context.subTitle,
                          ),

                        //! Product
                        _ProductPrice(
                          productQuantity: productQuantity,
                        ),
                      ],
                    ),
                  ),

                  context.mediumGap,

                  //! Quantity
                  Text("x${productQuantity.quantity}",
                      style: context.greyLabelMedium.copyWith(fontSize: 16)),
                ],
              ),
            )
          ],
        );
      },
    );
  }
}

class _ProductPrice extends StatelessWidget {
  final ProductQuantityModel productQuantity;

  const _ProductPrice({required this.productQuantity});

  @override
  Widget build(BuildContext context) {
    final product = productQuantity.product;
    final isSale = product!.isSale;

    final saleColor = context.isDark
        ? ColorManager.white.withOpacity(0.5)
        : ColorManager.black.withOpacity(0.5);

    final actualPrice = productQuantity.actualPrice;

    final sizePrice = productQuantity.price ?? product.totalPrice;

    return Row(
      children: [
        if (isSale) ...[
          Text(
            actualPrice.toCurrency(context),
            // product.salePrice.toCurrency(context),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style: context.subTitle.copyWith(color: ColorManager.primaryColor),
          ),
        ] else ...[
          Text(
            sizePrice.toCurrency(context),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style: context.subTitle.copyWith(color: ColorManager.primaryColor),
          ),
        ],
        if (isSale) ...[
          context.smallGap,
          Text(
            sizePrice.toCurrency(context),
            // product.totalPrice.toCurrency(context),
            style: context.labelMedium.copyWith(
                color: saleColor,
                decoration: TextDecoration.lineThrough,
                decorationThickness: 1.5,
                decorationColor: saleColor),
          ),
        ]
      ],
    );
  }
}
