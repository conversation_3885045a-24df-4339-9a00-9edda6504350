import 'package:colornames/colornames.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

baseTableText(
  String text, {
  required Font arabicFont,
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
  double? fontSize,
}) {
  var english = RegExp(r'[a-zA-Z]');

  var isEnglishText = english.hasMatch(text);

  return pw.Padding(
    padding: const pw.EdgeInsets.all(4),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textAlign: pw.TextAlign.center,
      textDirection:
          isLtr && isEnglishText ? pw.TextDirection.ltr : pw.TextDirection.rtl,
      style: pw.TextStyle(
        font: arabicFont,
        fontSize: fontSize,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}

String _currentCurrency(BuildContext context) {
  final vendorVM = context.read<AuthVM>();

  final isEnglish = context.read<AppSettingsVM>().locale.languageCode == 'en';

  final currentCurrency = vendorVM.currentVendor;

  final currency = currentCurrency?.config?.currencies.firstOrNull;

  final currencyName = isEnglish ? currency?.currencyEn : currency?.currencyAr;

  return currencyName ?? 'L.E';
}

String toInvoiceCurrency(BuildContext context, {required num? price}) {
  final currentCurrency = _currentCurrency(context);

  final num = price.removeDecimalZeroFormat(price ?? 0);

  final isEnglish = context.read<AppSettingsVM>().locale.languageCode == 'en';

  if (isEnglish) {
    return '$currentCurrency$num';
  }

  return '$num$currentCurrency';
}

Future<pw.Document> generateStoreOrderInvoicePDF(BuildContext appContext,
    {required OrderModel order, required bool isEnglish}) async {
  final currentVendor = appContext.read<AuthVM>().currentVendor;

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  // Helper functions to check if colors, sizes, and extras exist in the order
  bool hasColors =
      order.products?.any((product) => product.color.isNotEmpty) ?? false;
  bool hasSizes =
      order.products?.any((product) => product.size.isNotEmpty) ?? false;
  bool hasExtras =
      order.products?.any((product) => product.extras.isNotEmpty) ?? false;

  final pdf = pw.Document();

  englishTable() {
    // Build header row dynamically based on available data
    List<pw.Widget> headerCells = [
      baseTableText(
        appContext.tr.product,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ];

    if (hasSizes) {
      headerCells.add(baseTableText(
        appContext.tr.size,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasColors) {
      headerCells.add(baseTableText(
        appContext.tr.color,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasExtras) {
      headerCells.add(baseTableText(
        appContext.tr.extras,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    headerCells.addAll([
      baseTableText(
        appContext.tr.quantity,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.price,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.total,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ]);

    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: headerCells,
      ),
    ];

    // Add productQuantity details to the table
    for (var productQuantity in order.products!) {
      final productTotal =
          (productQuantity.actualPrice) * productQuantity.quantity!;

      // Build row cells dynamically based on available data
      List<pw.Widget> rowCells = [
        baseTableText(
          productQuantity?.nameByLang(appContext) ?? '-',
          arabicFont: arabicFont,
        ),
      ];

      if (hasSizes) {
        rowCells.add(baseTableText(
          productQuantity.size.isEmpty ? '-' : productQuantity.size,
          arabicFont: arabicFont,
        ));
      }

      if (hasColors) {
        rowCells.add(baseTableText(
          productQuantity.color.isEmpty
              ? '-'
              : ColorNames.guess(Color(productQuantity.color.toInt())),
          arabicFont: arabicFont,
        ));
      }

      if (hasExtras) {
        final extrasText = productQuantity.extras.isEmpty
            ? '-'
            : productQuantity.extras
                .map((extra) => '${extra['name']} (+${extra['price']})')
                .join(', ');
        rowCells.add(baseTableText(
          extrasText,
          arabicFont: arabicFont,
        ));
      }

      rowCells.addAll([
        baseTableText(
          productQuantity.quantity.toString(),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(appContext,
              price: productQuantity.price ?? productQuantity.actualPrice),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(appContext, price: productTotal),
          arabicFont: arabicFont,
        ),
      ]);

      children.add(
        pw.TableRow(
          children: rowCells,
        ),
      );
    }

    // Build column widths dynamically
    Map<int, pw.TableColumnWidth> columnWidths = {};
    int columnIndex = 0;

    // Product column
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

    // Size column (if exists)
    if (hasSizes) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    }

    // Color column (if exists)
    if (hasColors) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);
    }

    // Extras column (if exists)
    if (hasExtras) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(3);
    }

    // Quantity, Price, Total columns
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

    return pw.Table(
      columnWidths: columnWidths,
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  arabicTable() {
    //? reverse the order of the columns for Arabic
    // Build header row dynamically based on available data (reversed for Arabic)
    List<pw.Widget> headerCells = [
      baseTableText(
        appContext.tr.total,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.price,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.quantity,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ];

    if (hasColors) {
      headerCells.add(baseTableText(
        appContext.tr.color,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasSizes) {
      headerCells.add(baseTableText(
        appContext.tr.size,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasExtras) {
      headerCells.add(baseTableText(
        appContext.tr.extras,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    headerCells.add(baseTableText(
      appContext.tr.product,
      arabicFont: arabicFont,
      isBold: true,
    ));

    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: headerCells,
      ),
    ];

    // Add product details to the table
    for (var product in order.products!) {
      final productTotal = product.actualPrice * product.quantity!;

      // Build row cells dynamically based on available data (reversed for Arabic)
      List<pw.Widget> rowCells = [
        baseTableText(
          toInvoiceCurrency(appContext, price: productTotal),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(appContext, price: product.actualPrice),
          arabicFont: arabicFont,
        ),
        baseTableText(
          product.quantity.toString(),
          arabicFont: arabicFont,
        ),
      ];

      if (hasColors) {
        rowCells.add(baseTableText(
          product.color.isEmpty
              ? '-'
              : ColorNames.guess(Color(product.color.toInt())),
          arabicFont: arabicFont,
        ));
      }

      if (hasSizes) {
        rowCells.add(baseTableText(
          product.size.isEmpty ? '-' : product.size,
          arabicFont: arabicFont,
        ));
      }

      if (hasExtras) {
        final extrasText = product.extras.isEmpty
            ? '-'
            : product.extras
                .map((extra) =>
                    '${extra['name_ar'] ?? extra['name']} (+${extra['price']})')
                .join(', ');
        rowCells.add(baseTableText(
          extrasText,
          arabicFont: arabicFont,
        ));
      }

      rowCells.add(baseTableText(
        product.nameByLang(appContext),
        arabicFont: arabicFont,
      ));

      children.add(
        pw.TableRow(
          children: rowCells,
        ),
      );
    }

    // Build column widths dynamically for Arabic (reversed order)
    Map<int, pw.TableColumnWidth> columnWidths = {};
    int columnIndex = 0;

    // Total, Price, Quantity columns
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

    // Color column (if exists)
    if (hasColors) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);
    }

    // Size column (if exists)
    if (hasSizes) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    }

    // Extras column (if exists)
    if (hasExtras) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(3);
    }

    // Product column
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

    return pw.Table(
      columnWidths: columnWidths,
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  var vendorImage;

  try {
    vendorImage = await networkImage(currentVendor?.logo?.url ?? '');
  } catch (e) {}

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
              level: 0,
              child: pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 10),
                  child: pw.Align(
                      alignment: isEnglish
                          ? pw.Alignment.centerLeft
                          : pw.Alignment.centerRight,
                      child: pw.Column(
                        crossAxisAlignment: isEnglish
                            ? pw.CrossAxisAlignment.start
                            : pw.CrossAxisAlignment.end,
                        children: [
                          //! Vendor Logo --------------------------------
                          if (currentVendor?.logo?.url != null &&
                              (currentVendor?.logo?.url!.isNotEmpty ?? false))
                            pw.Center(
                              child: pw.Image(vendorImage,
                                  height: 180, fit: pw.BoxFit.contain),
                            )
                          else
                            pw.Center(
                              child: pw.Text(
                                currentVendor?.name ?? '',
                                textScaleFactor: 2,
                                textAlign: pw.TextAlign.center,
                                textDirection: pw.TextDirection.rtl,
                                style: pw.TextStyle(
                                  fontSize: 30,
                                  font: arabicFont,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ),

                          pw.Text(
                            '${appContext.tr.order_id}: ${isEnglish ? '#${order.orderId}' : '${order.orderId}#'}',
                            textScaleFactor: 2,
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            '${appContext.tr.dateAndTime}: ${order.createdAt?.formatDateToStringWithTime}',
                            textScaleFactor: 2,
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      )))),

          pw.SizedBox(height: 10),

          if (isEnglish) englishTable() else arabicTable(),

          pw.SizedBox(height: 10),

          pw.Divider(
            thickness: .9,
            color: PdfColors.black,
          ),

          pw.SizedBox(height: 10),

          //! Total Order Price & Bottom Section
          if (isEnglish)
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                ],
              ),
            )
          else
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ];
      },
    ),
  );

  return pdf;
}
