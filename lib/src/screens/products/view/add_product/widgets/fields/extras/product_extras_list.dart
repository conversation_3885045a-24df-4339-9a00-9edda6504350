import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/buttons/base_text_button.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/extras/choose_extra_card.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../../categories/models/category_model.dart';
import '../../../../../../dashboard/view/size_and_colors/size_and_color_screen.dart';
import '../../../../../../dashboard/view_model/extra_settings_view_model.dart';

class ProductExtrasList extends StatelessWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final Map<String, TextEditingController>? controllers;
  final List<ExtraSettingsModel>? productExtras;
  final CategoryModel category;
  final bool isSingle;
  final bool fromAddToCart;
  final Function(ExtraSettingsModel extra)? onSelected;

  const ProductExtrasList({
    super.key,
    required this.valueNotifiers,
    required this.category,
    this.controllers,
    this.onSelected,
    this.isSingle = false,
    this.fromAddToCart = false,
    this.productExtras,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ExtraSettingVM>(
      builder: (context, extraSettingsVM, child) {
        if (extraSettingsVM.isLoading) {
          return const LoadingWidget();
        }

        final selectedExtras = valueNotifiers[ApiStrings.extras]
            as ValueNotifier<List<ExtraSettingsModel>>;

        final extras = productExtras ??
            extraSettingsVM.extraSettings?.extras.where((element) => element
                .categories
                .map((e) => e.documentId)
                .contains(category.documentId)) ??
            [];

        if (extras.isEmpty) {
          return Column(
            children: [
              EmptyDataWidget(
                message: context.tr.noExtrasAdded,
              ),
              BaseTextButton(
                  title: context.tr.addExtras,
                  withUnderline: true,
                  onTap: () => context.to(
                      const SizeAndColorAndExtraScreen(selectedTabIndex: 2))),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(context.tr.extras, style: context.title),
                context.smallGap,
                const _AddExtrasButton(),
              ],
            ),
            context.mediumGap,
            StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        vertical: AppSpaces.xSmallPadding,
                      ),
                      decoration: BoxDecoration(
                        color: ColorManager.textFieldColor(context),
                        borderRadius: BorderRadius.circular(
                          AppRadius.baseRadius,
                        ),
                      ),
                      child: Wrap(
                        children: extras.mapIndexed((index, extra) {
                          final isExtraSelected = selectedExtras.value
                              .any((e) => e.englishName == extra.englishName);

                          return ChooseExtraCard(
                            extra: extra,
                            isSelected: isExtraSelected,
                          ).onTap(() {
                            setState(() {
                              if (!isSingle && !fromAddToCart) {
                                extra.price = null;
                              }

                              extraSettingsVM.selectOrUnSelectExtra(
                                selectedExtras: selectedExtras,
                                extra: productExtras?.firstWhereOrNull((e) =>
                                        e.englishName == extra.englishName) ??
                                    extra,
                                isSingle: isSingle,
                              );

                              if (onSelected != null) {
                                onSelected!(extra);
                              }
                            });
                          }).paddingSymmetric(
                            horizontal: AppSpaces.smallPadding,
                            vertical: AppSpaces.xSmallPadding,
                          );
                        }).toList(),
                      ),
                    ),
                    if (!fromAddToCart) ...[
                      context.mediumGap,
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: selectedExtras.value.length,
                        itemBuilder: (context, index) {
                          final extra = selectedExtras.value[index];

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${context.tr.extra}: ${extra.englishName ?? ''}',
                                textAlign: TextAlign.center,
                                style: context.title.copyWith(
                                  color: context.isDark
                                      ? ColorManager.white
                                      : ColorManager.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              context.smallGap,
                              BaseTextField(
                                isRequired: true,
                                title: context.tr.price,
                                textInputType: TextInputType.number,
                                initialValue: extra.price?.toString() ?? '',
                                hint: context.tr.price,
                                onChanged: (value) {
                                  extra.price = num.tryParse(value);
                                  selectedExtras.value
                                      .where((e) =>
                                          e.englishName == extra.englishName)
                                      .forEach((element) {
                                    element.price = extra.price;
                                  });
                                },
                              ),
                              context.smallGap,
                            ],
                          ).paddingSymmetric(
                            horizontal: AppSpaces.smallPadding,
                            vertical: AppSpaces.xSmallPadding,
                          );
                        },
                        separatorBuilder: (context, index) => const Divider(),
                      ),
                    ]
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}

class _AddExtrasButton extends StatelessWidget {
  const _AddExtrasButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          context.to(const SizeAndColorAndExtraScreen(selectedTabIndex: 2)),
      child: CircleAvatar(
        radius: 12,
        backgroundColor: ColorManager.primaryColor,
        child: Icon(
          Icons.add,
          color: ColorManager.white,
          size: 18.r,
        ),
      ),
    );
  }
}
