import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/colors/choose_color_card.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/sizes/choose_size_card.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_spaces.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../categories/models/category_model.dart';
import '../../../../categories/view_model/category_view_model.dart';
import '../../../view_model/products_view_model.dart';

class ProductDetails extends StatelessWidget {
  final ProductModel productModel;
  final CategoryModel category;

  const ProductDetails(
      {super.key, required this.productModel, required this.category});

  @override
  Widget build(BuildContext context) {
    final isSale = productModel.isSale;
    final saleColor = context.isDark
        ? ColorManager.white.withOpacity(0.5)
        : ColorManager.black.withOpacity(0.5);
    final productVM = context.read<ProductVM>();
    final catVM = context.read<CategoryVM>();

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //! product title
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: Text(
                        productVM.getProductTitle(
                                product: productModel, context: context) ??
                            '',
                        style: context.title)),

                //! product category
                Text(
                  catVM.getCategoryName(category: category, context: context) ??
                      '',
                  style: context.title.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.normal,
                  ),
                )
              ],
            ),
            //! price
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (isSale) ...[
                  Text(
                    productModel.salePrice.toCurrency(context),
                    style: context.subTitle,
                  ),
                ] else ...[
                  Text(
                    productModel.totalPrice.toCurrency(context),
                    style: context.subTitle,
                  )
                ],
                if (isSale) ...[
                  context.smallGap,
                  Text(
                    productModel.totalPrice.toCurrency(context),
                    style: context.labelMedium.copyWith(
                        color: saleColor,
                        decoration: TextDecoration.lineThrough,
                        decorationThickness: 2,
                        decorationColor: saleColor),
                  ),
                ]
              ],
            ),
          ],
        ),

        context.smallGap,

        //! Product description
        Text(productModel.englishDescription ?? '',
            style: context.labelLarge.copyWith(fontWeight: FontWeight.normal)),

        //! Inventory
        if (productModel.inventory != null) ...[
          context.largeGap,
          Row(
            children: [
              Icon(
                Icons.inventory_outlined,
                size: 30,
                color: context.isDark ? Colors.white : Colors.black,
              ),
              context.smallGap,
              Text(
                '${context.tr.inventory}: ${productModel.inventory}',
                style: context.title,
              ),
            ],
          ),
        ],

        context.largeGap,

        //! sizes & colors & extras
        SizesColorsAndExtrasList(productModel: productModel),
      ],
    );
  }
}

class SizesColorsAndExtrasList extends StatelessWidget {
  final ProductModel productModel;

  const SizesColorsAndExtrasList({super.key, required this.productModel});

  @override
  Widget build(BuildContext context) {
    final thereColors = productModel.colors.isNotEmpty;
    final thereSizes = productModel.sizes.isNotEmpty;
    final thereExtras = productModel.extras.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (thereSizes) ...[
          //! sizes
          Text(
            context.tr.sizes,
            style: context.labelLarge,
          ),

          context.smallGap,
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              ...productModel.sizes.map((size) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ChooseSizeCard(size: size),
                          if (size.price != null) ...[
                            context.smallGap,
                            Text(size.price!.toCurrency(context),
                                style: context.labelLarge),
                          ],
                        ],
                      ),
                      if (size.stock != null) ...[
                        context.smallGap,
                        Text(size.stock!.toString(), style: context.labelLarge)
                            .paddingSymmetric(
                          horizontal: AppSpaces.mediumPadding,
                        ),
                      ],
                    ],
                  ))
            ],
          ),
        ],
        context.largeGap,
        if (thereColors) ...[
          //! colors
          Text(
            thereColors ? context.tr.colors : context.tr.noColorsAdded,
            style: context.labelLarge,
          ),

          context.smallGap,
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              ...productModel.colors.map((color) => Column(
                    children: [
                      ChooseColorCard(
                        color: color,
                      ),
                      if (color.stock != null) ...[
                        context.smallGap,
                        Text(color.stock!.toString(),
                            style: context.labelLarge),
                      ],
                    ],
                  ))
            ],
          ),
        ],
        context.largeGap,
        if (thereExtras) ...[
          //! extras
          Text(
            context.tr.extras,
            style: context.labelLarge,
          ),
          context.smallGap,
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              ...productModel.extras.map((extra) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.mediumPadding,
                      vertical: AppSpaces.smallPadding,
                    ),
                    decoration: BoxDecoration(
                      color: ColorManager.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: ColorManager.primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          extra.nameByLang(context),
                          style: context.subTitle.copyWith(
                            color: ColorManager.primaryColor,
                          ),
                        ),
                        if (extra.price != null && extra.price! > 0)
                          Text(
                            extra.price.toCurrency(context),
                            style: context.labelMedium.copyWith(
                              color: ColorManager.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    ),
                  ))
            ],
          ),
        ],
      ],
    );
  }
}
