import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/add_to_cart/product_details_and_quantity_buttons.dart';
import 'package:provider/provider.dart';

import '../../../../../core/consts/api_strings.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../products/view/add_product/widgets/fields/colors/product_colors_list.dart';
import '../../../../products/view/add_product/widgets/fields/extras/product_extras_list.dart';
import '../../../../products/view/add_product/widgets/fields/sizes/product_sizes_list.dart';

class AddToCartWidgetDialog extends HookWidget {
  final ProductModel productModel;
  final Map<String, ValueNotifier> valueNotifiers;

  const AddToCartWidgetDialog(
      {super.key, required this.productModel, required this.valueNotifiers});

  @override
  Widget build(BuildContext context) {
    final quantity = useState(1);
    final isSale = productModel.isSale;

    final selectedSizePrice = useState<ExtraSettingsModel?>(
        (valueNotifiers[ApiStrings.sizes]?.value as List<ExtraSettingsModel>?)
            ?.firstOrNull);

    final selectedExtrasPrice = useState<List<ExtraSettingsModel>>(
        (valueNotifiers[ApiStrings.extras]?.value
                as List<ExtraSettingsModel>?) ??
            []);

    return Consumer<CartVM>(
      builder: (context, cartVM, child) {
        final extrasPrice = selectedExtrasPrice.value
            .fold<num>(0, (sum, extra) => sum + (extra.price ?? 0));
        final totalPrice =
            (selectedSizePrice.value?.price ?? productModel.totalPrice ?? 0) +
                extrasPrice;

        final totalSalePrice = (productModel.salePrice ?? 0) + extrasPrice;

        final finalPrice = (totalPrice * quantity.value);
        final finalSalePrice = (totalSalePrice * quantity.value);

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            // mainAxisSize: MainAxisSize.min,
            children: [
              ProductsDetailsAndQuantityButtons(
                productModel: productModel,
                quantity: quantity,
                totalPrice: finalPrice,
                salePrice: finalSalePrice,
              ),

              // * Colors ========================
              if (productModel.colors.isNotEmpty) ...[
                context.mediumGap,
                ProductColorsList(
                  isSingle: true,
                  // productColors: productModel.colors,
                  selectedColors: valueNotifiers[ApiStrings.colors]
                      as ValueNotifier<List<ExtraSettingsModel>>,
                  canAddStock: false,
                ),
              ],

              // * Sizes ========================
              if (productModel.sizes.isNotEmpty) ...[
                context.mediumGap,
                ProductSizesList(
                  category: productModel.category!,
                  productSize: productModel.sizes,
                  isSingle: true,
                  valueNotifiers: valueNotifiers,
                  fromAddToCart: true,
                  canAddStock: false,
                  onSelected: (selectedSize) {
                    selectedSizePrice.value = selectedSize;
                  },
                ),
              ],

              // * Extras ========================
              if (productModel.extras.isNotEmpty) ...[
                context.mediumGap,
                ProductExtrasList(
                  category: productModel.category!,
                  productExtras: productModel.extras,
                  valueNotifiers: valueNotifiers,
                  fromAddToCart: true,
                  onSelected: (selectedExtra) {
                    final currentExtras = List<ExtraSettingsModel>.from(
                        selectedExtrasPrice.value);
                    final isSelected = currentExtras
                        .any((e) => e.englishName == selectedExtra.englishName);

                    if (isSelected) {
                      currentExtras.removeWhere(
                          (e) => e.englishName == selectedExtra.englishName);
                    } else {
                      currentExtras.add(selectedExtra);
                    }

                    selectedExtrasPrice.value = currentExtras;

                    // Also update the ValueNotifier for UI consistency
                    (valueNotifiers[ApiStrings.extras]
                            as ValueNotifier<List<ExtraSettingsModel>>?)
                        ?.value = currentExtras;
                  },
                ),
              ],

              context.largeGap,
              // * Add Product Button ========================
              Button(
                  isLoading: false,
                  label: context.tr.addToCart,
                  onPressed: () async {
                    await cartVM.saveProductToCart(context,
                        productQuantity: ProductQuantityModel(
                            quantity: quantity.value,
                            totalPrice: isSale ? finalSalePrice : finalPrice,
                            price: totalPrice,
                            product: productModel,
                            color: (valueNotifiers[ApiStrings.colors]?.value
                                        as List<ExtraSettingsModel>?)
                                    ?.firstOrNull
                                    ?.englishName ??
                                '',
                            size: (valueNotifiers[ApiStrings.sizes]?.value
                                        as List<ExtraSettingsModel>?)
                                    ?.firstOrNull
                                    ?.englishName ??
                                '',
                            extras: selectedExtrasPrice.value));
                  })
            ],
          ),
        );
      },
    );
  }
}

// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
// import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
// import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
// import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
// import 'package:idea2app_vendor_app/src/screens/store/view/widgets/add_to_cart/product_details_and_quantity_buttons.dart';
// import 'package:provider/provider.dart';
//
// import '../../../../../core/consts/api_strings.dart';
// import '../../../../../core/shared_widgets/shared_widgets.dart';
// import '../../../../products/view/add_product/widgets/fields/colors/product_colors_list.dart';
// import '../../../../products/view/add_product/widgets/fields/extras/product_extras_list.dart';
// import '../../../../products/view/add_product/widgets/fields/sizes/product_sizes_list.dart';
//
// class AddToCartWidgetDialog extends HookWidget {
//   final ProductModel productModel;
//   final Map<String, ValueNotifier> valueNotifiers;
//
//   const AddToCartWidgetDialog(
//       {super.key, required this.productModel, required this.valueNotifiers});
//
//   @override
//   Widget build(BuildContext context) {
//     final quantity = useState(1);
//     final isSale = productModel.isSale;
//
//     final selectedSizePrice = useState<ExtraSettingsModel?>(
//         (valueNotifiers[ApiStrings.sizes]?.value as List<ExtraSettingsModel>?)
//             ?.firstOrNull);
//
//     // Initialize valueNotifiers[ApiStrings.extras] if it doesn't exist
//     useEffect(() {
//       if (valueNotifiers[ApiStrings.extras] == null) {
//         valueNotifiers[ApiStrings.extras] =
//             ValueNotifier<List<ExtraSettingsModel>>([]);
//       }
//       return null;
//     }, []);
//
//     return Consumer<CartVM>(
//       builder: (context, cartVM, child) {
//         // Calculate extras price directly from valueNotifiers
//         final selectedExtras = (valueNotifiers[ApiStrings.extras]?.value
//                 as List<ExtraSettingsModel>?) ??
//             [];
//         final extrasPrice = selectedExtras.fold<num>(
//             0, (sum, extra) => sum + (extra.price ?? 0));
//
//         final totalPrice =
//             (selectedSizePrice.value?.price ?? productModel.totalPrice ?? 0) +
//                 extrasPrice;
//         final totalSalePrice = (productModel.salePrice ?? 0) + extrasPrice;
//
//         final finalPrice = (totalPrice * quantity.value);
//         final finalSalePrice = (totalSalePrice * quantity.value);
//
//         return SingleChildScrollView(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             // mainAxisSize: MainAxisSize.min,
//             children: [
//               ProductsDetailsAndQuantityButtons(
//                 productModel: productModel,
//                 quantity: quantity,
//                 totalPrice: finalPrice,
//                 salePrice: finalSalePrice,
//               ),
//
//               // * Colors ========================
//               if (productModel.colors.isNotEmpty) ...[
//                 context.mediumGap,
//                 ProductColorsList(
//                   isSingle: true,
//                   // productColors: productModel.colors,
//                   selectedColors: valueNotifiers[ApiStrings.colors]
//                       as ValueNotifier<List<ExtraSettingsModel>>,
//                   canAddStock: false,
//                 ),
//               ],
//
//               // * Sizes ========================
//               if (productModel.sizes.isNotEmpty) ...[
//                 context.mediumGap,
//                 ProductSizesList(
//                   category: productModel.category!,
//                   productSize: productModel.sizes,
//                   isSingle: true,
//                   valueNotifiers: valueNotifiers,
//                   fromAddToCart: true,
//                   canAddStock: false,
//                   onSelected: (selectedSize) {
//                     selectedSizePrice.value = selectedSize;
//                   },
//                 ),
//               ],
//
//               // * Extras ========================
//               if (productModel.extras.isNotEmpty) ...[
//                 context.mediumGap,
//                 ProductExtrasList(
//                   category: productModel.category!,
//                   productExtras: productModel.extras,
//                   valueNotifiers: valueNotifiers,
//                   fromAddToCart: true,
//                   onSelected: (selectedExtra) {
//                     final currentExtras =
//                         List<ExtraSettingsModel>.from(selectedExtras);
//                     final isSelected = currentExtras
//                         .any((e) => e.englishName == selectedExtra.englishName);
//
//                     if (isSelected) {
//                       currentExtras.removeWhere(
//                           (e) => e.englishName == selectedExtra.englishName);
//                     } else {
//                       currentExtras.add(selectedExtra);
//                     }
//
//                     // selectedExtras = currentExtras;
//
//                     // Also update the ValueNotifier for UI consistency
//                     (valueNotifiers[ApiStrings.extras]
//                             as ValueNotifier<List<ExtraSettingsModel>>?)
//                         ?.value = currentExtras;
//
//                     cartVM.notifyListeners();
//                   },
//                 ),
//               ],
//
//               context.largeGap,
//               // * Add Product Button ========================
//               Button(
//                   isLoading: false,
//                   label: context.tr.addToCart,
//                   onPressed: () async {
//                     await cartVM.saveProductToCart(context,
//                         productQuantity: ProductQuantityModel(
//                             quantity: quantity.value,
//                             totalPrice: isSale ? finalSalePrice : finalPrice,
//                             price: totalPrice,
//                             product: productModel,
//                             color: (valueNotifiers[ApiStrings.colors]?.value
//                                         as List<ExtraSettingsModel>?)
//                                     ?.firstOrNull
//                                     ?.englishName ??
//                                 '',
//                             size: (valueNotifiers[ApiStrings.sizes]?.value
//                                         as List<ExtraSettingsModel>?)
//                                     ?.firstOrNull
//                                     ?.englishName ??
//                                 '',
//                             extras: selectedExtras
//                                 .map((extra) => {
//                                       'name': extra.englishName ?? '',
//                                       'name_ar': extra.arabicName ?? '',
//                                       'price': extra.price ?? 0,
//                                     })
//                                 .toList()));
//                   })
//             ],
//           ),
//         );
//       },
//     );
//   }
// }
