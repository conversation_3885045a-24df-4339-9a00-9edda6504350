import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';

class ExtraSettingsModel {
  final int? id;
  final String? englishName;
  final String? arabicName;
  int? stock;
  num? price;
  final List<CategoryModel> categories;

  ExtraSettingsModel({
    this.id,
    this.englishName = '',
    this.arabicName = '',
    this.stock,
    this.price,
    this.categories = const [],
  });

  // name by lang
  //  String nameByLang(BuildContext context) {
  //     final isEnglish = context.readIsEng;
  //
  //     return isEnglish
  //         ? (englishTitle?.isNotEmpty == true ? englishTitle! : arabicTitle ?? '')
  //         : (arabicTitle?.isNotEmpty == true ? arabicTitle! : englishTitle ?? '');
  //   }
  String nameByLang(BuildContext context) {
    final isEnglish = context.readIsEng;

    return isEnglish
        ? (englishName?.isNotEmpty == true ? englishName! : arabicName ?? '')
        : (arabicName?.isNotEmpty == true ? arabicName! : englishName ?? '');
  }

  factory ExtraSettingsModel.fromJson(Map<String, dynamic> json) {
    final categories = (json[ApiStrings.categories] as List?) ?? [];

    final categoriesList = categories
        .map((e) => e.runtimeType == int
            ? CategoryModel(id: e)
            : CategoryModel.fromJson(e))
        .toList();

    return ExtraSettingsModel(
      id: json[ApiStrings.id],
      englishName: json[ApiStrings.name] ?? '',
      arabicName: json[ApiStrings.nameAr] ?? '',
      stock: int.tryParse(json[ApiStrings.stock].toString()) ?? 0,
      price: json[ApiStrings.price],
      categories: categoriesList,
    );
  }

  factory ExtraSettingsModel.fromProductJson(Map<String, dynamic> json) {
    return ExtraSettingsModel(
      englishName: json[ApiStrings.name] ?? '',
      arabicName: json[ApiStrings.nameAr] ?? '',
      stock: int.tryParse(json[ApiStrings.stock].toString()) ?? 0,
      price: json[ApiStrings.price],
    );
  }

  // copyWith
  ExtraSettingsModel copyWith({
    int? id,
    String? name,
    String? arabicName,
    String? englishName,
    int? stock,
    num? price,
    List<CategoryModel>? categories,
    bool canStockBeNull = false,
  }) {
    return ExtraSettingsModel(
      id: id ?? this.id,
      englishName: name ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      stock: canStockBeNull ? null : stock ?? this.stock,
      price: price ?? this.price,
      categories: categories ?? this.categories,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (englishName != null) ApiStrings.name: englishName,
      if (arabicName != null) ApiStrings.nameAr: arabicName,
      ApiStrings.categories: categories.map((e) => e.id).toList(),
      ApiStrings.vendor: VendorModelHelper.currentVendorId(),
      ApiStrings.price: price,
    };
  }

  //? To Product Json
  Map<String, dynamic> toProductJson({bool withStock = true}) {
    return {
      ApiStrings.name: englishName,
      ApiStrings.nameAr: arabicName,
      if (stock != null && withStock) ApiStrings.stock: stock?.toString(),
      ApiStrings.price: price,
    };
  }

  @override
  String toString() {
    return toJson().toString();
  }
}
