import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:provider/provider.dart';

import '../../models/extra_setting_model.dart';
import '../../view_model/extra_settings_view_model.dart';

class AddExtrasScreen extends HookWidget {
  const AddExtrasScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedCat = useState<List<CategoryModel>>([]);

    final extraValues = useState<List<Map<String, dynamic>>>([]);

    final englishExtraController = useTextEditingController();
    final arabicExtraController = useTextEditingController();

    final extraSettingsVM = context.read<ExtraSettingVM>();

    final formKey = useState(GlobalKey<FormState>());

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.extras,
        haveBackButton: true,
      ),
      body: StatefulBuilder(builder: (context, setState) {
        return Form(
          key: formKey.value,
          child: ListView(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            children: [
              CategoriesDropDown(
                isMulti: true,
                selectedMultiCategories: selectedCat,
              ),
              context.largeGap,
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        BaseTextField(
                          controller: englishExtraController,
                          title: context.tr.englishName,
                          hint: context.tr.englishName,
                        ),
                        context.smallGap,
                        BaseTextField(
                          controller: arabicExtraController,
                          title: context.tr.arabicName,
                          hint: context.tr.arabicName,
                        ),
                      ],
                    ),
                  ),
                  context.smallGap,
                  IconButton(
                      style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                              ColorManager.primaryColor)),
                      onPressed: () {
                        if (!formKey.value.currentState!.validate()) return;
                        final extra = {
                          'name': englishExtraController.text,
                          'arabic_ar': arabicExtraController.text,
                        };

                        // final checkIfExist = extraValues.value
                        //     .map((e) => e['englishName'])
                        //     .contains(englishExtraController.text);

                        // if (checkIfExist) {
                        //   context.showBarMessage('Extra already exists',
                        //       isError: true);
                        // } else {
                        extraValues.value.add(extra);

                        englishExtraController.clear();
                        arabicExtraController.clear();

                        setState(() {});
                        // }
                      },
                      icon: Icon(
                        Icons.add,
                        color: ColorManager.white,
                      )),
                ],
              ),
              context.mediumGap,
              if (extraValues.value.isNotEmpty) ...[
                Text(context.tr.extras, style: context.labelLarge),
                context.mediumGap,
                ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: extraValues.value.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    final englishName =
                        extraValues.value[index]['name']?.toString() ?? '';
                    final arabicName =
                        extraValues.value[index]['name_ar']?.toString() ?? '';

                    return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: AppSpaces.smallPadding + 3,
                            vertical: AppSpaces.xSmallPadding),
                        decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(AppRadius.baseRadius),
                            border: Border.all(
                                color: context.isDark
                                    ? ColorManager.white
                                    : ColorManager.black,
                                width: 1)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (englishName.isNotEmpty)
                                    Text(
                                      '${context.tr.english}: $englishName',
                                      style: context.labelMedium,
                                    ),
                                  if (englishName.isNotEmpty &&
                                      arabicName.isNotEmpty)
                                    context.smallGap,
                                  if (arabicName.isNotEmpty)
                                    Text(
                                      '${context.tr.arabic}: $arabicName',
                                      style: context.labelMedium,
                                    ),
                                ],
                              ),
                            ),
                            IconButton(
                                onPressed: () {
                                  extraValues.value.removeAt(index);
                                  setState(() {});
                                },
                                icon: const Icon(
                                  Icons.remove_circle,
                                  color: Colors.red,
                                ))
                          ],
                        ));
                  },
                  separatorBuilder: (context, index) => context.smallGap,
                ),
              ]
            ],
          ),
        );
      }),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(
          top: AppSpaces.xSmallPadding,
          left: AppSpaces.mediumPadding,
          right: AppSpaces.mediumPadding,
          bottom: AppSpaces.largePadding,
        ),
        child: Selector<ExtraSettingVM, bool>(
          selector: (context, authVM) => authVM.isLoading,
          builder: (context, value, child) {
            return Button(
              isLoading: value,
              label: context.tr.save,
              onPressed: () {
                if (selectedCat.value.isEmpty) {
                  context.showBarMessage(context.tr.selectCategories,
                      isError: true);
                  return;
                }
                if (extraValues.value.isEmpty) {
                  context.showBarMessage(context.tr.noExtrasAdded,
                      isWarning: true);
                  return;
                }
                List<ExtraSettingsModel> extras = [];

                extras.addAll(extraSettingsVM.extraSettings!.extras);

                final newExtras = extraValues.value
                    .map((e) => ExtraSettingsModel(
                        englishName: e['name'] ?? '',
                        arabicName: e['name_ar'] ?? '',
                        categories: selectedCat.value))
                    .toList();

                extras.addAll(newExtras);

                final copiedWithExtraSettings =
                    extraSettingsVM.extraSettings!.copyWith(
                  extras: extras,
                );

                extraSettingsVM.addExtraSetting(context,
                    extraSettings: copiedWithExtraSettings, isBack: true);
              },
            );
          },
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
