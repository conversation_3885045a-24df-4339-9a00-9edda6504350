class ApiStrings {
  static const int timeOutDuration = 30;

  // headers
  static const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static const String id = 'id';
  static const String documentId = 'documentId';
  static const String orderId = 'order_id';

  static const String data = 'data';

  static const String createdAt = 'createdAt';
  static const String thumbnail = 'thumbnail';
  static const String products = 'products';
  static const String images = 'images';
  static const String image = 'image';

  // websiteName
  static const String websiteName = 'website_name';

  // templates
  static const String templates = 'templates';
  static const String productCategories = 'product_categories';
  static const String categories = 'categories';

  static const String mainCategory = 'main_category';
  static const String mainCategories = "main-categories";
  static const String discount = 'discount';
  static const String brands = 'brands';
  static const String vendor = 'vendor';

  //? Fields
  static const String user = 'user';
  static const String checkUpdate = 'check_update';
  static const String stock = 'stock';
  static const String invoiceId = 'invoice_id';
  static const String isActiveWorkingTime = 'is_active_working_time';
  static const String lat = 'lat';
  static const String lng = 'lng';
  static const String bannerLimit = 'banner_limit';
  static const String version = 'version';
  static const String fromStore = 'from_store';
  static const String guestPhoneNumber = 'phone_number';
  static const String guestName = 'guest_name';
  static const String shippings = 'shippings';
  static const String citiesCost = 'cities_cost';
  static const String cities = 'cities';
  static const String country = 'country';
  static const String countries = 'countries';
  static const String location = 'location';
  static const String media = 'media';
  static const String cart = 'cart';
  static const String banner = 'banner';
  static const String freeShipping = 'free_shipping';
  static const String type = 'type';
  static const String url = 'url';
  static const String name = 'name';
  static const String nameEn = 'name_en';
  static const String nameAr = 'name_ar';
  static const String showPricing = 'show_pricing';
  static const String orderRingingBell = 'order_ringing_bell';
  static const String attachment = 'attachment';
  static const String paymentAttachment = 'payment_attachment';
  static const String isPaid = 'is_paid';
  static const String isDone = 'is_done';
  static const String username = 'username';
  static const String displayName = 'displayName';
  static const String link = 'link';
  static const String deviceToken = 'device_token';
  static const String deviceType = 'device_type';
  static const String businessType = 'business_type';
  static const String config = 'config';
  static const String pricing = 'pricing';
  static const String pricingPlan = 'pricing_plan';
  static const String websiteLink = 'website_link';
  static const String minimumOrderCost = 'minimum_order_cost';
  static const String currencies = 'currencies';
  static const String primaryColor = 'primary_color';
  static const String defaultLanguage = 'default_language';
  static const String defaultTheme = 'default_theme';
  static const String expireDate = 'expire_date';
  static const String startDate = 'start_date';
  static const String paidAmount = 'paid_amount';
  static const String size = 'size';
  static const String sizes = 'sizes';
  static const String color = 'color';
  static const String colors = 'colors';
  static const String extras = 'extras';
  static const String currencyEn = 'currency_en';
  static const String currencyAr = 'currency_ar';
  static const String symbol = 'symbol';
  static const String email = 'email';
  static const String phone = 'phone';
  static const String businessName = 'business_name';
  static const String featureImage = 'feature_image';
  static const String description = 'description';

  // phone_number
  static const String phoneNumber = 'phone_number';
  static const String title = 'title';
  static const String inventoryEnabled = 'inventory_enabled';
  static const String isSizeColorInventory = 'is_size_color_inventory';
  static const String arabicTitle = 'title_ar';
  static const String arabicDescription = 'description_ar';
  static const String promo = 'promo';
  static const String body = 'body';
  static const String cost = 'cost';
  static const String deliveryCost = 'delivery_cost';
  static const String outOfStock = 'is_out_of_stock';
  static const String isInStock = 'is_in_stock';
  static const String isFeatured = 'is_featured';
  static const String isActive = 'is_active';
  static const String isPercent = 'is_percent';
  static const String showInList = 'show_in_list';
  static const String inventory = 'inventory';
  static const String price = 'price';
  static const String minQuantitySaleNumber = 'min_quantity_sale_number';
  static const String isQuantitySalePercentage = 'is_quantity_sale_percentage';
  static const String quantitySale = 'quantity_sale';
  static const String priceDollar = 'price_dollar';
  static const String ordersCount = 'orders_count';
  static const String extraOrderPrice = 'extra_order_price';
  static const String salePrice = 'sale_price';
  static const String review = 'review';
  static const String isSale = 'is_sale';
  static const String total = 'total';
  static const String totalPrice = 'totalPrice';
  static const String payment = 'payment';
  static const String shipping = 'shipping';
  static const String promoCode = 'promo_code';
  static const String sort = 'sort';
  static const String status = 'order_status';
  static const String pending = 'Pending';
  static const String active = 'active';
  static const String address = 'address';
  static const String priceAmount = 'paid_amount';
  static const String password = 'password';
  static const String logo = 'logo';
  static const String vendorType = 'vendorType';
  static const String apartment = 'apartment';
  static const String floor = 'floor';
  static const String building = 'building';
  static const String streetName = 'street_name';
  static const String city = 'city';
  static const String state = 'state';
  static const String quantity = "quantity";
  static const String product = "product";
  static const String productsQuantity = "products_quantity";
  static const String note = "note";
  static const String termsEn = "terms_en";
  static const String termsAr = "terms_ar";
  static const String areas = "areas";
  static const String aboutEn = "about_en";
  static const String aboutAr = "about_ar";
  static const String facebook = "facebook";
  static const String whatsapp = "whatsapp";
  static const String website = "website";
  static const String tiktok = "tiktok";
  static const String instagram = "instagram";
  static const String contactUs = "contact_us";
  static const String youtube = "youtube";
  static const String isApproved = 'is_approved';
  static const String paymentMethod = 'payment_method';
  static const String about = 'about';
  static const String whatsApp = 'whatsApp';
  static const String qrLanding = 'qr_landing';
  static const String appStoreLink = 'app_store_link';
  static const String globalSale = 'global_sale';
  static const String isGlobalSalePercentage = 'is_global_sale_percentage';
  static const String playStoreLink = 'play_store_link';
  static const String vendorWebViewFeatures = 'vendor_webview_features';
  static const String canceledOrders = 'canceled_orders';
  static const String refundedOrders = 'refunded_orders';
  static const String totalOrders = 'total_orders';
  static const String totalEarning = 'total_earning';
  static const String icon = 'icon';
  static const String roles = 'roles';
  static const String parentVendor = 'parent_vendor';
  static const String subVendors = 'sub_vendors';
  static const String vendorVideos = 'vendor_videos';

  // Checkout Settings
  static const String checkoutSettings = 'checkout_settings';
  static const String checkoutType = 'checkout_type';
  static const String showCheckout = 'show_checkout';
  static const String guestCheckout = 'guest_checkout';
  static const String show = 'show';
  static const String isRequired = 'is_required';
}
