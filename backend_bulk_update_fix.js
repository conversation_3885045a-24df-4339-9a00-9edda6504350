// Fixed backend function for Strapi v5
async bulkUpdate(ctx: Context) {
  try {
    const { data } = ctx.request.body;

    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Invalid request body. Expected "data" object with documentIds as keys.');
    }

    const results = [];
    const documentIds = Object.keys(data);

    if (documentIds.length === 0) {
      return ctx.badRequest('No products to update. Please provide at least one documentId.');
    }

    // Process each product update
    for (const documentId of documentIds) {
      try {
        const updateData = data[documentId];

        if (!updateData || typeof updateData !== 'object') {
          results.push({
            documentId,
            success: false,
            error: 'Invalid update data for this documentId'
          });
          continue;
        }

        // Check if product exists using documentId (not id)
        const existingProduct = await strapi.documents('api::product.product').findOne({
          documentId: documentId
        });

        if (!existingProduct) {
          results.push({
            documentId,
            success: false,
            error: 'Product not found'
          });
          continue;
        }

        // Update the product using documentId
        const updatedProduct = await strapi.documents('api::product.product').update({
          documentId: documentId,
          data: updateData
        });

        results.push({
          documentId,
          success: true,
          data: formatProduct(updatedProduct)
        });

      } catch (error) {
        console.error(`Error updating product ${documentId}:`, error);
        results.push({
          documentId,
          success: false,
          error: error.message || 'Unknown error occurred'
        });
      }
    }

    // Calculate summary
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    ctx.send({
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount
      },
      results
    });

  } catch (err) {
    console.error('Error in bulkUpdate method:', err);
    return ctx.badRequest({ message: 'Error updating products', error: err.message });
  }
}
