plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.dashboard.idea2app_vendor_app"
    compileSdkVersion 35
    ndkVersion "28.0.13004108"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        applicationId "com.idea2app.vendor_app"
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // Remove the following line if using Flutter 3.22 and Dart 3.4
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10"
}
//def localProperties = new Properties()
//def localPropertiesFile = rootProject.file('local.properties')
//if (localPropertiesFile.exists()) {
//    localPropertiesFile.withReader('UTF-8') { reader ->
//        localProperties.load(reader)
//    }
//}
//
//def flutterRoot = localProperties.getProperty('flutter.sdk')
//if (flutterRoot == null) {
//    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
//}
//
//def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
//if (flutterVersionCode == null) {
//    flutterVersionCode = '1'
//}
//
//def flutterVersionName = localProperties.getProperty('flutter.versionName')
//if (flutterVersionName == null) {
//    flutterVersionName = '1.0'
//}
//
//apply plugin: 'com.android.application'
//// START: FlutterFire Configuration
//apply plugin: 'com.google.gms.google-services'
//// END: FlutterFire Configuration
//apply plugin: 'kotlin-android'
//apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
//
//def keystoreProperties = new Properties()
//def keystorePropertiesFile = rootProject.file('key.properties')
//if (keystorePropertiesFile.exists()) {
//    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
//}
//
//android {
//    namespace "com.dashboard.idea2app_vendor_app"
//    compileSdkVersion flutter.compileSdkVersion
//    ndkVersion "28.0.13004108"
//
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }
//
//    kotlinOptions {
//        jvmTarget = '1.8'
//    }
//
//    sourceSets {
//        main.java.srcDirs += 'src/main/kotlin'
//    }
//
//    defaultConfig {
//        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
//        applicationId "com.idea2app.vendor_app"
////        applicationId "com.dashboard.idea2app_vendor_app"
//        // You can update the following values to match your application needs.
//        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
//        minSdkVersion 24
//        targetSdkVersion flutter.targetSdkVersion
//        versionCode flutterVersionCode.toInteger()
//        versionName flutterVersionName
//    }
//
//    signingConfigs {
//        release {
//            keyAlias keystoreProperties['keyAlias']
//            keyPassword keystoreProperties['keyPassword']
//            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
//            storePassword keystoreProperties['storePassword']
//        }
//    }
//    buildTypes {
//        release {
//            signingConfig signingConfigs.release
//        }
//    }
//}
//
//flutter {
//    source '../..'
//}
//
//dependencies {
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//}
