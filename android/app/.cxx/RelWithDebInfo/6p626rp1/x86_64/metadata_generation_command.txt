                        -H/Users/<USER>/.shorebird/bin/cache/flutter/a5bd04b23bd448f5c825a20ddb391110ea15d3f1/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMA<PERSON>_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/6p626rp1/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/6p626rp1/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/RelWithDebInfo/6p626rp1/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2