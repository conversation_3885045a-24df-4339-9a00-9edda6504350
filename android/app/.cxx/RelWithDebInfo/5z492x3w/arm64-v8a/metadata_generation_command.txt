                        -H/Users/<USER>/.shorebird/bin/cache/flutter/b14323b05f28b2c72b34f57c8059ec3001d65712/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/28.0.13004108/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/5z492x3w/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/build/app/intermediates/cxx/RelWithDebInfo/5z492x3w/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/RelWithDebInfo/5z492x3w/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2