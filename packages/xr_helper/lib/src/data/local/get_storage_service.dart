part of xr_helper;

class GetStorageService {
  static final _box = GetStorage();

  static Future<void> init() async {
    await GetStorage.init();
  }

  static Future<bool> setData(
      {required String key, required dynamic value}) async {
    try {
      await _box.write(key, value);

      XRLogger.i("Local data saved successfully");
      return true;
    } on Exception catch (e) {
      XRLogger.e("Error while saving local data $e");
      return false;
    }
  }

  static dynamic getData({required dynamic key}) {
    return _box.read(key);
  }

  Future<bool> removeLocalData({required String key}) async {
    try {
      await _box.remove(key);

      XRLogger.i("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      XRLogger.e("Error while removing local data $e");
      return false;
    }
  }

  static bool hasData({required String key}) {
    return _box.hasData(key);
  }

  static Future<bool> clearLocalData() async {
    try {
      await _box.erase();

      XRLogger.i("Local data cleared successfully");
      return true;
    } on Exception catch (e) {
      XRLogger.e("Error while clearing local data $e");
      return false;
    }
  }

  //? Remove Key
  static Future<bool> removeKey({required String key}) async {
    try {
      await _box.remove(key);

      XRLogger.i("Local data removed successfully");
      return true;
    } on Exception catch (e) {
      XRLogger.e("Error while removing local data $e");
      return false;
    }
  }
}
