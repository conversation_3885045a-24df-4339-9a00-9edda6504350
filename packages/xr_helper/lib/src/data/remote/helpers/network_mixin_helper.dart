// part of xr_helper;
//
// mixin XNetworkHelper {
//   // * Get headers helper ================================
//   Map<String, String> get headers => {
//         'Content-Type': 'application/json',
//         'Accept': 'application/json',
//         'x-org-key': '1',
//         'x-api-key': '123456',
//         if (GetStorage().hasData(LocalKeys.token))
//           'Authorization': "Bearer ${GetStorage().read(LocalKeys.token)}"
//       };
//
//   // * Return response helper ================================
//   dynamic returnResponse(http.Response response) {
//     switch (response.statusCode) {
//       case 200:
//         return jsonDecode(response.body.toString());
//       case 400:
//         throw FetchDataException(response.toString());
//       case 401:
//       case 403:
//         throw UnauthorisedException(response.body.toString());
//       case 404:
//         throw UnauthorisedException(response.body.toString());
//       case 408:
//         throw FetchDataException(ApiMessages.timeOutError);
//       case 422:
//         throw FetchDataException(ApiMessages.timeOutError);
//       case 500:
//       default:
//         throw FetchDataException(
//             'Error occurred while communication with server: ${response.body} with status code : ${response.statusCode}');
//     }
//   }
// }
