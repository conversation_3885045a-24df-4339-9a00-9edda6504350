// part of xr_helper;
//
// class AppException implements Exception {
//   final String? _message;
//
//   AppException([
//     this._message,
//   ]);
//
//   @override
//   String toString() {
//     return "$_message";
//   }
// }
//
// class FetchDataException extends AppException {
//   FetchDataException([super.message]);
// }
//
//
// class UnauthorisedException extends AppException {
//   UnauthorisedException([super._message]);
// }
