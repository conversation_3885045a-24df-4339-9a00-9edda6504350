part of xr_helper;

class BaseController extends ChangeNotifier {
  //! Loading ====================================
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set setIsLoading(bool isLoading) {
    _isLoading = isLoading;
    notifyListeners();
  }

  //! Base Function With Exception Handling ====================================
  Future<dynamic> baseControllerFunction(
    Function() mainFunction, {
    bool isBack = true,
    bool isLoading = true,
    Function()? additionalFunction,
    Function(dynamic error)? onError,
  }) async {
    try {
      setIsLoading = isLoading;

      final function = await mainFunction();

      await _handleSuccess(
        isBack: isBack,
        additionalFunction: additionalFunction,
      );

      return function;
    }
    // on FetchDataException catch (error) {
    //   Log.e(error.toString());
    //
    //   setIsLoading = false;
    //
    //   if (onError != null) {
    //     onError(error);
    //   }
    // }
    on SocketException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showFlushBar(type: FlushBarType.noInternet);
      // }
    } on TimeoutException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showFlushBar(type: FlushBarType.timeOut);
      // }
    } catch (error, s) {
      XRLogger.e('$error\n$s');
      setIsLoading = false;
    }
  }

  Future<void> _handleSuccess(
      {bool isBack = true, Function()? additionalFunction}) async {
    if (additionalFunction != null) {
      await additionalFunction();
    }

    setIsLoading = false;
  }
}
