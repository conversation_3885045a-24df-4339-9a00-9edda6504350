// part of xr_helper;
//
// class NotificationService {
//   static Future<void> _firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     if (Firebase.apps.isEmpty) await Firebase.initializeApp();
//   }
//
//   static void init() async {
//     final fcm = FirebaseMessaging.instance;
//
//     await fcm.requestPermission(
//       alert: true,
//       badge: true,
//       provisional: false,
//       sound: true,
//     );
//
//     fcm.setForegroundNotificationPresentationOptions(
//         badge: true, alert: true, sound: true);
//
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
//
//
//     return;
//   }
//
//   //? Get Token
//   static Future<String> getToken() async {
//     final fcm = FirebaseMessaging.instance;
//
//     final token = await fcm.getToken();
//
//     return token ?? '';
//   }
//
//   //? Subscribe to topic
//   static Future<void> subscribeToTopic(String topic) async {
//     final fcm = FirebaseMessaging.instance;
//
//     Log.w('SUBSCRIBED TO $topic');
//
//     await fcm.subscribeToTopic(topic);
//   }
//
//   //! Send Notification
//   static Future sendNotification(String serverKey, {
//     required String title,
//     required String body,
//     required String userToken,
//     bool isTopic = false,
//   }) async {
//     final data = jsonEncode(
//       <String, dynamic>{
//         'notification': <String, dynamic>{
//           'title': title,
//           'body': body,
//         },
//         'priority': 'high',
//         'data': <String, dynamic>{
//           'click_action': 'FLUTTER_NOTIFICATION_CLICK',
//           'id': '1',
//           'status': 'done'
//         },
//         'to': isTopic ? '/topics/$userToken' : userToken,
//       },
//     );
//
//     final response = await http.post(
//       Uri.parse('https://fcm.googleapis.com/fcm/send'),
//       headers: <String, String>{
//         'Content-Type': 'application/json',
//         'Authorization': 'key=$serverKey',
//       },
//       body: data,
//     );
//
//     Log.w('notificationResponse: ${response.body}');
//   }
//
//
//   static void listenToNotifications({
//     Function(RemoteMessage)? onMessage,
//     Function(RemoteMessage)? onMessageOpenedApp,
//   }) {
//     FirebaseMessaging.onMessage.listen((message) {
//       Log.i('onMessage:\nTitle ${message.notification?.title}\nBody ${message
//           .notification?.body}\nData ${message.data}');
//
//       if (onMessage != null) onMessage(message);
//     });
//
//     FirebaseMessaging.onMessageOpenedApp.listen((message) {
//       Log.i('onOpenAppMessage:\nTitle ${message.notification
//           ?.title}\nBody ${message.notification?.body}\nData ${message.data}');
//
//       if (onMessageOpenedApp != null) onMessageOpenedApp(message);
//     });
//   }
// }
