# https://www.dartlang.org/guides/language/analysis-options
# Source of linter options:
# http://dart-lang.github.io/linter/lints/options/options.html
analyzer:
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: warning

linter:
  rules:
    # these rules are documented on and in the same order as
    # the Dart Lint rules page to make maintenance easier
    # https://github.com/dart-lang/linter/blob/master/example/all.yaml
    # - always_declare_return_types
    # - always_specify_types
    # - annotate_overrides
    # - avoid_as
    - avoid_empty_else
    - avoid_init_to_null
    - avoid_return_types_on_setters
    - await_only_futures
    - camel_case_types
    - cancel_subscriptions
    - close_sinks
    # - comment_references # we do not presume as to what people want to reference in their dartdocs
    # - constant_identifier_names # https://github.com/dart-lang/linter/issues/204
    - control_flow_in_finally
    - empty_constructor_bodies
    - empty_statements
    - hash_and_equals
    - implementation_imports
    # - invariant_booleans
    # - iterable_contains_unrelated_type
    - library_names
    # - library_prefixes
    # - list_remove_unrelated_type
    # - literal_only_boolean_expressions
    - non_constant_identifier_names
    # - one_member_abstracts
    # - only_throw_errors
    # - overridden_fields
    - package_api_docs
    - package_names
    - package_prefixed_library_names
    - prefer_is_not_empty
    # - prefer_mixin # https://github.com/dart-lang/language/issues/32
    # - public_member_api_docs
    - slash_for_doc_comments
    # - sort_constructors_first
    # - sort_unnamed_constructors_first
    - test_types_in_equals
    - throw_in_finally
    # - type_annotate_public_apis # subset of always_specify_types
    - type_init_formals
    # - unawaited_futures
    - unnecessary_brace_in_string_interps
    - unnecessary_getters_setters
    - unnecessary_statements
    - unrelated_type_equality_checks
    - valid_regexps